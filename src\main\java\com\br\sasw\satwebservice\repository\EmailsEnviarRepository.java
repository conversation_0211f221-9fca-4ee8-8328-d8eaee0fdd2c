package com.br.sasw.satwebservice.repository;

import com.br.sasw.satwebservice.entity.EmailsEnviar;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

@Repository
public interface EmailsEnviarRepository extends JpaRepository<EmailsEnviar, BigDecimal> {

    @Query("SELECT e FROM EmailsEnviar e WHERE e.flagEnviado IS NULL OR e.flagEnviado = '' ORDER BY e.sequencia")
    List<EmailsEnviar> findEmailsPendentes();

    @Query("SELECT e FROM EmailsEnviar e WHERE e.flagEnviado <> '*' AND e.parametro IN :parametros ORDER BY e.sequencia")
    List<EmailsEnviar> findEmailsByParametros(@Param("parametros") List<String> parametros);

    @Query("SELECT e FROM EmailsEnviar e WHERE e.flagEnviado <> '*' AND (e.parametro NOT IN :parametrosExcluidos OR e.parametro IS NULL) ORDER BY e.sequencia")
    List<EmailsEnviar> findEmailsExcluindoParametros(@Param("parametrosExcluidos") List<String> parametrosExcluidos);

    @Query("SELECT e FROM EmailsEnviar e WHERE e.codCli = :codCli AND e.codFil = :codFil AND e.parametro = :parametro " +
           "AND (:dataInicio IS NULL OR e.dtInclusao >= :dataInicio) " +
           "AND (:dataFim IS NULL OR e.dtInclusao <= :dataFim) " +
           "ORDER BY e.sequencia DESC")
    List<EmailsEnviar> findEmailsCliente(@Param("codCli") String codCli, 
                                        @Param("codFil") String codFil, 
                                        @Param("parametro") String parametro,
                                        @Param("dataInicio") LocalDate dataInicio, 
                                        @Param("dataFim") LocalDate dataFim);

    @Query("SELECT COUNT(e) FROM EmailsEnviar e WHERE e.flagEnviado IS NULL OR e.flagEnviado = ''")
    long countEmailsPendentes();

    @Query("SELECT COUNT(e) FROM EmailsEnviar e WHERE e.dtEnvio = :data")
    Long findEmailsEnviadosNaData(@Param("data") LocalDate data);

    @Query("SELECT COUNT(e) FROM EmailsEnviar e WHERE e.flagEnviado = 'e' AND e.dtEnvio = :data")
    Long findEmailsComFalha(@Param("data") LocalDate data);

    @Modifying
    @Query("UPDATE EmailsEnviar e SET e.flagEnviado = '*', e.dtEnvio = :dtEnvio, e.hrEnvio = :hrEnvio WHERE e.sequencia = :sequencia")
    void marcarComoEnviado(@Param("sequencia") BigDecimal sequencia,
                          @Param("dtEnvio") LocalDate dtEnvio,
                          @Param("hrEnvio") String hrEnvio);


    @Modifying
    @Query("UPDATE EmailsEnviar e SET e.flagEnviado = 'e' WHERE e.sequencia = :sequencia")
    void marcarComoFalha(@Param("sequencia") BigDecimal sequencia);


    @Modifying
    @Query("UPDATE EmailsEnviar e SET e.flagEnviado = '', e.dtEnvio = null, e.hrEnvio = '' WHERE e.sequencia = :sequencia")
    void marcarComoPendente(@Param("sequencia") BigDecimal sequencia);
}
