package com.br.sasw.satwebservice.service;

import com.br.sasw.satwebservice.util.XmlSigner;
import lombok.RequiredArgsConstructor;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.ws.transport.http.HttpComponentsMessageSender;

import java.nio.charset.StandardCharsets;


@Service
@RequiredArgsConstructor
public class ReinfService {

    @Value("${reinf.envio.url}")
    private String envioUrl;

    @Value("${reinf.consulta.url}")
    private String consultaUrl;

    private final HttpClientService httpClientService;

    public String send(String xml, String id, String empresa, String nrInscEmpregador, String tpInscEmpregador, String nrInscTransmissor, String tpInscTransmissor){

        String signedXml = XmlSigner.sign(xml, empresa);
        String eventoCompleto = buildEvent(signedXml, id, nrInscEmpregador, tpInscEmpregador, nrInscTransmissor,tpInscTransmissor);

        HttpPost httpPost = new HttpPost(envioUrl);
        httpPost.addHeader("Content-Type", "application/xml");
        httpPost.addHeader("Accept", "application/xml");
        byte[] xmlBytes = eventoCompleto.getBytes(StandardCharsets.UTF_8);
        int contentLength = xmlBytes.length;
        httpPost.setHeader("Content-Length", String.valueOf(contentLength));
        try {
            httpPost.setEntity(new StringEntity(eventoCompleto));
            CloseableHttpClient httpClient = httpClientService.createHttpClientForEmpresa(empresa);

            HttpComponentsMessageSender messageSender = new HttpComponentsMessageSender();
            messageSender.setHttpClient(httpClient);

            CloseableHttpResponse resp = httpClient.execute(httpPost);

            return resp != null
                    ? EntityUtils.toString(resp.getEntity(), "UTF-8")
                    : eventoCompleto;

        } catch(Exception e) {
            return eventoCompleto;
        }
    }

    public String build(String xml, String id, String empresa, String nrInscEmpregador, String tpInscEmpregador) {
        String signedXml = XmlSigner.sign(xml, empresa);
        return buildEvent(signedXml, id, nrInscEmpregador, tpInscEmpregador, null, null);
    }

    private String buildEvent(String xmlAssinado, String id, String nrInscEmpregador, String tpInscEmpregador, String nrInscTransmissor, String tpInscTransmissor){
        StringBuilder builder = new StringBuilder();
        builder.append("<?xml version=\"1.0\" encoding=\"UTF-8\"?>");
        builder.append("<Reinf xmlns=\"http://www.reinf.esocial.gov.br/schemas/envioLoteEventosAssincrono/v1_00_00\">");
        builder.append("<envioLoteEventos>");
        builder.append("<ideContribuinte>");
        builder.append("<tpInsc>${tpInscEmpregador}</tpInsc>");
        builder.append("<nrInsc>${nrInscEmpregador}</nrInsc>");
        builder.append("</ideContribuinte>");
        builder.append("<eventos>");
        builder.append("<evento Id=\"${id}\">");
        builder.append(xmlAssinado);
        builder.append("</evento>");
        builder.append("</eventos>");
        builder.append("</envioLoteEventos>");
        builder.append("</Reinf>");

        String evento = builder.toString();

        evento = evento.replace("${id}", id);
        evento = evento.replace("${nrInscEmpregador}", nrInscEmpregador);
        evento = evento.replace("${tpInscEmpregador}", tpInscEmpregador);
        return evento;
    }

    public String get(String protocolo, String empresa) {

        try {
            CloseableHttpClient httpClient = httpClientService.createHttpClientForEmpresa(empresa);

            HttpComponentsMessageSender messageSender = new HttpComponentsMessageSender();
            messageSender.setHttpClient(httpClient);

            HttpGet httpGet = new HttpGet(consultaUrl + protocolo);
            httpGet.setHeader("Content-Type", "application/xml; charset=utf-8");

            CloseableHttpResponse resp = httpClient.execute(httpGet);

            return resp != null
                    ? EntityUtils.toString(resp.getEntity(), "UTF-8")
                    : "";


        } catch (Exception e) {
            e.printStackTrace();
        }

        return null;
    }
}
