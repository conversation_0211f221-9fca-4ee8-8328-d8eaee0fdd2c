package com.br.sasw.satwebservice.repository;

import com.br.sasw.satwebservice.entity.EmailsEnviarAnexo;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.List;

@Repository
public interface EmailsEnviarAnexoRepository extends JpaRepository<EmailsEnviarAnexo, Long> {

    @Query("SELECT a FROM EmailsEnviarAnexo a WHERE a.sequencia = :sequencia ORDER BY a.ordem")
    List<EmailsEnviarAnexo> findBySequenciaOrderByOrdem(@Param("sequencia") BigDecimal sequencia);

    @Modifying
    @Query("DELETE FROM EmailsEnviarAnexo a WHERE a.sequencia = :sequencia")
    void deleteBySequencia(@Param("sequencia") BigDecimal sequencia);


    @Query("SELECT COUNT(a) FROM EmailsEnviarAnexo a WHERE a.sequencia = :sequencia")
    long countBySequencia(@Param("sequencia") BigDecimal sequencia);

    List<EmailsEnviarAnexo> findBySequencia(BigDecimal sequencia);
}
