package com.br.sasw.satwebservice.service;

import com.br.sasw.satwebservice.util.NFSeJoaoPessoaXmlSigner;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.ws.transport.http.HttpComponentsMessageSender;

import java.nio.charset.StandardCharsets;

@Service
@RequiredArgsConstructor
@Slf4j
public class NFSEJoaoPessoaService {

    @Value("${nfse.joaopessoa.url:https://receita.joaopessoa.pb.gov.br/notafiscal-abrasfv203-ws/NotaFiscalSoap}")
    private String url;

    @Value("${nfse.joaopessoa.envio.action:GerarNfse}")
    private String envioAction;

    @Value("${nfse.joaopessoa.consulta.action:ConsultarNfseRps}")
    private String consultaAction;

    private final HttpClientService httpClientService;

    public String send(String xml, String empresa) {
        try {
            log.debug("Iniciando envio de NFSe para João Pessoa - Empresa: {}", empresa);

            String xmlAssinado = NFSeJoaoPessoaXmlSigner.sign(xml, empresa);
            log.debug("XML assinado com sucesso");

            String soapEnv = String.format("""
                <soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:nfse="http://nfse.abrasf.org.br">
                   <soapenv:Header/>
                   <soapenv:Body>
                      %s
                   </soapenv:Body>
                </soapenv:Envelope>""", xmlAssinado);

            CloseableHttpClient httpClient = httpClientService.createHttpClientForEmpresa(empresa);
            HttpComponentsMessageSender messageSender = new HttpComponentsMessageSender();
            messageSender.setHttpClient(httpClient);

            HttpPost httpPost = new HttpPost(url);
            httpPost.setHeader("SOAPAction", envioAction);
            httpPost.setHeader("Content-Type", "text/xml; charset=utf-8");
            
            byte[] xmlBytes = soapEnv.getBytes(StandardCharsets.UTF_8);
            int contentLength = xmlBytes.length;
            httpPost.setHeader("Content-Length", String.valueOf(contentLength));

            StringEntity entity = new StringEntity(soapEnv, "UTF-8");
            httpPost.setEntity(entity);

            log.debug("Enviando requisição SOAP para: {}", url);
            CloseableHttpResponse resp = httpClient.execute(httpPost);

            String response = resp != null
                    ? EntityUtils.toString(resp.getEntity(), "UTF-8")
                    : "";

            log.debug("Resposta recebida do serviço de João Pessoa");
            return response;

        } catch(Exception e) {
            log.error("Erro ao enviar NFSe para João Pessoa", e);
            return "Erro ao enviar NFSe para João Pessoa: " + e.getMessage();
        }
    }

    public String get(String xml, String empresa) {
        try {
            log.debug("Iniciando consulta de NFSe para João Pessoa - Empresa: {}", empresa);

            String soapEnv = String.format("""
                <soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:nfse="http://nfse.abrasf.org.br">
                   <soapenv:Header/>
                   <soapenv:Body>
                      <nfse:ConsultarNfseRps>
                         %s
                      </nfse:ConsultarNfseRps>
                   </soapenv:Body>
                </soapenv:Envelope>""", xml);

            CloseableHttpClient httpClient = httpClientService.createHttpClientForEmpresa(empresa);
            HttpComponentsMessageSender messageSender = new HttpComponentsMessageSender();
            messageSender.setHttpClient(httpClient);

            HttpPost httpPost = new HttpPost(url);
            httpPost.setHeader("SOAPAction", consultaAction);
            httpPost.setHeader("Content-Type", "text/xml; charset=utf-8");
            
            byte[] xmlBytes = soapEnv.getBytes(StandardCharsets.UTF_8);
            int contentLength = xmlBytes.length;
            httpPost.setHeader("Content-Length", String.valueOf(contentLength));

            StringEntity entity = new StringEntity(soapEnv, "UTF-8");
            httpPost.setEntity(entity);

            log.debug("Enviando consulta SOAP para: {}", url);
            CloseableHttpResponse resp = httpClient.execute(httpPost);

            String response = resp != null
                    ? EntityUtils.toString(resp.getEntity(), "UTF-8")
                    : "";

            log.debug("Resposta de consulta recebida do serviço de João Pessoa");
            return response;

        } catch(Exception e) {
            log.error("Erro ao consultar NFSe em João Pessoa", e);
            return "Erro ao consultar NFSe em João Pessoa: " + e.getMessage();
        }
    }
}
