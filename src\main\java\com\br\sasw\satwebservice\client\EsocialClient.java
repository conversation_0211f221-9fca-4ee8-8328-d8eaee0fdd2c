package com.br.sasw.satwebservice.client;

import br.gov.esocial.servicos.empregador.lote.eventos.envio.v1_1_0.EnviarLoteEventos;
import br.gov.esocial.servicos.empregador.lote.eventos.envio.v1_1_0.EnviarLoteEventosResponse;
import com.br.sasw.satwebservice.config.SoapConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.ws.client.core.WebServiceTemplate;
import org.springframework.ws.client.core.support.WebServiceGatewaySupport;
import org.springframework.ws.soap.client.core.SoapActionCallback;
import org.w3c.dom.Document;
import org.xml.sax.InputSource;

import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import java.io.StringReader;

@Service
public class EsocialClient extends WebServiceGatewaySupport {

    @Autowired
    private SoapConfig.WebServiceTemplateFactory webServiceTemplateFactory;

    @Value("${esocial.envio.url}")
    private String url;

    @Value("${esocial.envio.action}")
    private String action;

    public EnviarLoteEventosResponse enviarLoteEventos(String xmlLoteEventos, String empresa) {
        try {
            WebServiceTemplate empresaWebServiceTemplate = webServiceTemplateFactory.createForEmpresa(empresa);

            EnviarLoteEventos request = new EnviarLoteEventos();

            org.w3c.dom.Element xmlElement = criarElementoXml(xmlLoteEventos);
            request.setLoteEventos(new EnviarLoteEventos.LoteEventos());
            request.getLoteEventos().setAny(xmlElement);

            String soapAction = action;

            return (EnviarLoteEventosResponse) empresaWebServiceTemplate
                    .marshalSendAndReceive(
                            url,
                            request,
                            new SoapActionCallback(soapAction)
                    );
        } catch (Exception e) {
            throw new RuntimeException("Erro ao enviar lote de eventos para empresa: " + empresa, e);
        }
    }
    
    private org.w3c.dom.Element criarElementoXml(String xmlString) {
        try {
            DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
            factory.setNamespaceAware(true);
            DocumentBuilder builder = factory.newDocumentBuilder();
            Document document = builder.parse(new InputSource(new StringReader(xmlString)));
            return document.getDocumentElement();
        } catch (Exception e) {
            throw new RuntimeException("Erro ao criar elemento XML", e);
        }
    }
}