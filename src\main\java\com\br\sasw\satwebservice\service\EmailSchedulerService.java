package com.br.sasw.satwebservice.service;

import com.br.sasw.satwebservice.config.EmailConfig;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
@Slf4j
public class EmailSchedulerService {

    private final EmailService emailService;
    private final EmailConfig emailConfig;


    @Value("${email.scheduler-enabled}")
    private Boolean schedulerAtivo;

    @Scheduled(fixedDelayString = "#{@emailConfig.schedulerIntervalMs}")
    public void processarEmailsAgendado() {
        if (!schedulerAtivo) {
            log.info("Scheduler de emails está desativado");
            return;
        }


        try {
            log.info("Iniciando processamento agendado de emails");
            emailService.processarEmailsPendentes();
            
        } catch (Exception e) {
            log.info("Erro no processamento agendado de emails: {}", e.getMessage(), e);
            
        } finally {
            log.info("Processamento agendado de emails finalizado");
        }
    }

    public SchedulerStatus getStatus() {
        return new SchedulerStatus(
            schedulerAtivo,
            emailConfig.getSchedulerIntervalMs(),
            emailService.getEmailStats()
        );
    }

    @Data
    @AllArgsConstructor
    public class SchedulerStatus {
        private Boolean ativo;
        private Long intervalMs;
        private EmailService.EmailStats emailStats;
    }
}
