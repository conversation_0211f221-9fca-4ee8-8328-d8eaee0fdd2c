# Correções para NFSe João Pessoa - Erro de Assinatura

## Problemas Identificados

### 1. Campo ExigibilidadeISS Faltante
**Problema**: O WSDL de João Pessoa exige o campo `<ExigibilidadeISS>` (linha 747 do WSDL), mas ele não estava presente no XML original.

**Solução**: Adicionado automaticamente o campo `<ExigibilidadeISS>1</ExigibilidadeISS>` após o `<CodigoMunicipio>`.

### 2. Referência da Assinatura Incorreta
**Problema**: A assinatura estava usando uma referência vazia (`URI=""`), mas João Pess<PERSON> exige que a referência aponte para o Id do elemento `InfDeclaracaoPrestacaoServico`.

**Solução**: Alterada a referência para `URI="#0626384900013400008408"` (usando o Id do elemento).

### 3. Assinador Específico para João Pessoa
**Problema**: Estava usando o assinador de Goiânia (`NFSeGoianiaXmlSigner`) que não é compatível com o padrão ABRASF de João Pessoa.

**Solução**: Criado um novo assinador específico `NFSeJoaoPessoaXmlSigner` que segue o padrão correto.

## Arquivos Criados/Modificados

### 1. NFSeJoaoPessoaXmlSigner.java
- Assinador específico para João Pessoa
- Adiciona automaticamente o campo `ExigibilidadeISS` se não existir
- Usa a referência correta para o Id do `InfDeclaracaoPrestacaoServico`
- Segue o padrão ABRASF v2.03

### 2. NFSEJoaoPessoaService.java
- Serviço específico para João Pessoa
- Usa o assinador correto
- Configura o envelope SOAP adequado

### 3. NFSEJoaoPessoaController.java
- Controller específico com endpoints `/nfse-joaopessoa/envio` e `/nfse-joaopessoa/consulta`

### 4. TesteController.java (Modificado)
- Alterado para usar o novo assinador de João Pessoa

### 5. application.properties (Atualizado)
- Adicionadas configurações específicas para João Pessoa

## Como Testar

### 1. Usando o TesteController (Endpoint de Teste)
```bash
POST http://localhost:8080/nfse-jp
Content-Type: application/xml

# Corpo da requisição: seu XML sem assinatura
# Parâmetro: ?empresa=SUA_EMPRESA
```

### 2. Usando o Controller Completo
```bash
POST http://localhost:8080/nfse-joaopessoa/envio?empresa=SUA_EMPRESA
Content-Type: application/xml

# Corpo da requisição: seu XML sem assinatura
```

### 3. XML de Exemplo Corrigido
Veja o arquivo `exemplo-xml-joao-pessoa-corrigido.xml` para a estrutura correta.

## Principais Diferenças na Assinatura

### Antes (Incorreto)
```xml
<Reference URI="">
    <Transforms>
        <Transform Algorithm="http://www.w3.org/2000/09/xmldsig#enveloped-signature"/>
        <Transform Algorithm="http://www.w3.org/TR/2001/REC-xml-c14n-20010315"/>
    </Transforms>
    <DigestMethod Algorithm="http://www.w3.org/2001/04/xmlenc#sha256"/>
    <DigestValue>...</DigestValue>
</Reference>
```

### Depois (Correto)
```xml
<Reference URI="#0626384900013400008408">
    <Transforms>
        <Transform Algorithm="http://www.w3.org/2000/09/xmldsig#enveloped-signature"/>
        <Transform Algorithm="http://www.w3.org/TR/2001/REC-xml-c14n-20010315"/>
    </Transforms>
    <DigestMethod Algorithm="http://www.w3.org/2001/04/xmlenc#sha256"/>
    <DigestValue>...</DigestValue>
</Reference>
```

## Configurações Adicionadas

```properties
# NFSE JOAO PESSOA
nfse.joaopessoa.url=https://receita.joaopessoa.pb.gov.br/notafiscal-abrasfv203-ws/NotaFiscalSoap
nfse.joaopessoa.envio.action=GerarNfse
nfse.joaopessoa.consulta.action=ConsultarNfseRps
```

## Próximos Passos

1. **Teste a assinatura**: Use o endpoint `/nfse-jp` para testar apenas a assinatura
2. **Teste o envio completo**: Use o endpoint `/nfse-joaopessoa/envio` para enviar para João Pessoa
3. **Verifique os logs**: Os logs mostrarão detalhes do processo de assinatura
4. **Analise a resposta**: Se ainda houver erro, analise a resposta do serviço de João Pessoa

## Observações Importantes

- O campo `ExigibilidadeISS` é obrigatório para João Pessoa (valor 1 = Exigível no município)
- A referência da assinatura deve sempre apontar para o Id do `InfDeclaracaoPrestacaoServico`
- O certificado deve estar configurado corretamente no sistema
- O envelope SOAP deve seguir o padrão ABRASF v2.03

## Logs para Monitoramento

O sistema agora inclui logs detalhados:
- Início da assinatura
- Carregamento do certificado
- Correção automática do XML
- Sucesso da assinatura
- Envio da requisição SOAP
- Resposta do serviço
