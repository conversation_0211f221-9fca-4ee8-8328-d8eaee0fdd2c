package com.br.sasw.satwebservice.controller;

import com.br.sasw.satwebservice.service.NFSEJoaoPessoaService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/nfse-joaopessoa")
@RequiredArgsConstructor
public class NFSEJoaoPessoaController {

    private final NFSEJoaoPessoaService service;

    @PostMapping("/envio")
    public String send(@RequestBody String xml, @RequestParam String empresa) {
        return service.send(xml, empresa);
    }

    @PostMapping("/consulta")
    public String get(@RequestBody String xml, @RequestParam String empresa) {
        return service.get(xml, empresa);
    }
}
