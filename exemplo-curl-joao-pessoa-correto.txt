curl --location 'https://receita.joaopessoa.pb.gov.br/notafiscal-abrasfv203-ws/NotaFiscalSoap' \
--header 'Content-Type: text/xml' \
--header 'SOAPAction: https://receita.joaopessoa.pb.gov.br/notafiscal-abrasfv203-ws/GerarNfse' \
--data '<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/">
    <soapenv:Header/>
    <soapenv:Body>
        <GerarNfse>
            <GerarNfseEnvio>
                <Rps>
                    <InfDeclaracaoPrestacaoServico Id="RPS0626384900013400008408-ASSINA ESTE ID">
                        <Rps Id="">
                            <IdentificacaoRps>
                                <Numero>8408</Numero>
                                <Serie>RPS</Serie>
                                <Tipo>1</Tipo>
                            </IdentificacaoRps>
                            <DataEmissao>2025-06-27</DataEmissao>
                            <Status>1</Status>
                        </Rps>
                        <Competencia>2025-06-27</Competencia>
                        <Servico>
                            <Valores>
                                <ValorServicos>1461.17</ValorServicos>
                                <ValorDeducoes>0.00</ValorDeducoes>
                                <ValorPis>0.00</ValorPis>
                                <ValorCofins>0.00</ValorCofins>
                                <ValorInss>0.00</ValorInss>
                                <ValorIr>0.00</ValorIr>
                                <ValorCsll>0.00</ValorCsll>
                                <OutrasRetencoes>0.00</OutrasRetencoes>
                                <ValorIss>73.06</ValorIss>
                                <Aliquota>5.00</Aliquota>
                                <DescontoIncondicionado>0.00</DescontoIncondicionado>
                                <DescontoCondicionado>0.00</DescontoCondicionado>
                            </Valores>
                            <IssRetido>2</IssRetido>
                            <ItemListaServico>11.04</ItemListaServico>
                            <CodigoCnae>1104</CodigoCnae>
                            <Discriminacao>TRIBUTOS ACRESCIDOS SOBRE VALOR SERVICO          
 PREPARACAO DE NUMERARIO          
 REFERENTE AOS SERVICOS PRESTADOS DE   PROCESSAMENTO DE NUMERARIO, CONFORME   RELATORIO ANEXO.   Referente ao periodo: 21/05/2025 A 20/06/2025|Vencimento: 09/07/2025</Discriminacao>
                            <CodigoMunicipio>2507507</CodigoMunicipio>
                            <ExigibilidadeISS>1</ExigibilidadeISS>
                            <MunicipioIncidencia>2507507</MunicipioIncidencia>
                        </Servico>
                        <Prestador>
                            <CpfCnpj>
                                <Cnpj>06263849000134</Cnpj>
                            </CpfCnpj>
                            <InscricaoMunicipal>925781</InscricaoMunicipal>
                        </Prestador>
                        <Tomador>
                            <IdentificacaoTomador>
                                <CpfCnpj>
                                    <Cnpj>36618436000196</Cnpj>
                                </CpfCnpj>
                            </IdentificacaoTomador>
                            <RazaoSocial>ATACAMIX COMERCIO ATACADISTA E VAREJISTA DE ALIMENTOS LTDA</RazaoSocial>
                            <Endereco>
                                <Endereco>ROD PB</Endereco>
                                <Numero>S/N</Numero>
                                <Complemento>N/D</Complemento>
                                <Bairro>TERRA NOVA</Bairro>
                                <CodigoMunicipio>2515302</CodigoMunicipio>
                                <Uf>PB</Uf>
                                <Cep>58340000</Cep>
                            </Endereco>
                        </Tomador>
                        <OptanteSimplesNacional>2</OptanteSimplesNacional>
                        <IncentivoFiscal>2</IncentivoFiscal>
                    </InfDeclaracaoPrestacaoServico>
                    <Signature xmlns="http://www.w3.org/2000/09/xmldsig#">
                        <SignedInfo>
                            <CanonicalizationMethod Algorithm="http://www.w3.org/TR/2001/REC-xml-c14n-20010315"/>
                            <SignatureMethod Algorithm="http://www.w3.org/2000/09/xmldsig#rsa-sha1"/>
                            <Reference URI="#RPS0626384900013400008408-ASSINA ESTE ID">
                                <Transforms>
                                    <Transform Algorithm="http://www.w3.org/2000/09/xmldsig#enveloped-signature"/>
                                    <Transform Algorithm="http://www.w3.org/TR/2001/REC-xml-c14n-20010315"/>
                                </Transforms>
                                <DigestMethod Algorithm="http://www.w3.org/2000/09/xmldsig#sha1"/>
                                <DigestValue>DIGEST_VALUE_AQUI</DigestValue>
                            </Reference>
                        </SignedInfo>
                        <SignatureValue>SIGNATURE_VALUE_AQUI</SignatureValue>
                        <KeyInfo>
                            <X509Data>
                                <X509Certificate>CERTIFICADO_AQUI</X509Certificate>
                            </X509Data>
                        </KeyInfo>
                    </Signature>
                </Rps>
            </GerarNfseEnvio>
        </GerarNfse>
    </soapenv:Body>
</soapenv:Envelope>'
