package com.br.sasw.satwebservice.config;

import com.br.sasw.satwebservice.service.HttpClientService;
import org.apache.http.impl.client.CloseableHttpClient;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.oxm.jaxb.Jaxb2Marshaller;
import org.springframework.ws.client.core.WebServiceTemplate;
import org.springframework.ws.transport.http.HttpComponentsMessageSender;

@Configuration
public class SoapConfig {

    @Bean
    public Jaxb2Marshaller marshaller() {
        Jaxb2Marshaller marshaller = new Jaxb2Marshaller();
        marshaller.setContextPath("br.gov.esocial.servicos.empregador.lote.eventos.envio.v1_1_0");
        return marshaller;
    }


    @Bean
    public WebServiceTemplateFactory webServiceTemplateFactory(HttpClientService httpClientService) {
        return new WebServiceTemplateFactory(marshaller(), httpClientService);
    }


    public static class WebServiceTemplateFactory {

        private final Jaxb2Marshaller marshaller;
        private final HttpClientService httpClientService;

        public WebServiceTemplateFactory(Jaxb2Marshaller marshaller, HttpClientService httpClientService) {
            this.marshaller = marshaller;
            this.httpClientService = httpClientService;
        }

        public WebServiceTemplate createForEmpresa(String empresa) throws Exception {
            WebServiceTemplate webServiceTemplate = new WebServiceTemplate();
            webServiceTemplate.setMarshaller(marshaller);
            webServiceTemplate.setUnmarshaller(marshaller);
            webServiceTemplate.setDefaultUri("https://webservices.envio.esocial.gov.br/servicos/empregador/enviarloteeventos/WsEnviarLoteEventos.svc");
            webServiceTemplate.setMessageSender(createHttpComponentsMessageSender(empresa));
            return webServiceTemplate;
        }

        private HttpComponentsMessageSender createHttpComponentsMessageSender(String empresa) throws Exception {
            CloseableHttpClient httpClient = httpClientService.createHttpClientForEmpresa(empresa);

            HttpComponentsMessageSender messageSender = new HttpComponentsMessageSender();
            messageSender.setHttpClient(httpClient);

            return messageSender;
        }
    }
}