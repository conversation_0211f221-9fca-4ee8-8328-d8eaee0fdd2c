@echo off
setlocal enabledelayedexpansion

echo ========================================
echo  SatWebService Novo - Com Progresso
echo ========================================

REM Configuracao do Java local
set "LOCAL_JAVA_HOME=C:\Program Files\Java\jdk-17.0.15+6"
set "MAVEN_HOME=%~dp0tools\maven"

REM Configura as variaveis de ambiente
set "JAVA_HOME=%LOCAL_JAVA_HOME%"
set "PATH=%LOCAL_JAVA_HOME%\bin;%MAVEN_HOME%\bin;%PATH%"
set "M2_HOME=%MAVEN_HOME%"

echo Usando Java: %JAVA_HOME%
echo Usando Maven: %MAVEN_HOME%
echo.

echo ========================================
echo  Iniciando compilacao...
echo ========================================
echo.
echo Tempo de inicio: %TIME%
echo.

REM Cria arquivo de log temporario
set "LOG_FILE=%TEMP%\maven_build.log"

REM Executa Maven em background e salva log
echo Executando: mvn clean package -DskipTests
echo.
echo Progresso (atualizando a cada 10 segundos):
echo.

start /b "Maven Build" cmd /c ""%MAVEN_HOME%\bin\mvn.cmd" clean package -DskipTests -Dmaven.repo.local=.m2\repository > "%LOG_FILE%" 2>&1"

REM Monitora o progresso
set "COUNTER=0"
:MONITOR_LOOP
timeout /t 10 /nobreak >nul

REM Verifica se o processo ainda esta rodando
tasklist /FI "WINDOWTITLE eq Maven Build" 2>nul | find "cmd.exe" >nul
if %ERRORLEVEL% neq 0 goto BUILD_FINISHED

REM Mostra progresso
set /a COUNTER+=10
echo [%TIME%] Compilando ha %COUNTER% segundos...

REM Mostra ultimas linhas do log se existir
if exist "%LOG_FILE%" (
    echo Ultima atividade:
    powershell -Command "Get-Content '%LOG_FILE%' | Select-Object -Last 2"
    echo.
)

goto MONITOR_LOOP

:BUILD_FINISHED
echo.
echo ========================================
echo  Compilacao finalizada!
echo ========================================
echo Tempo final: %TIME%
echo.

REM Verifica resultado
if exist "%LOG_FILE%" (
    findstr /C:"BUILD SUCCESS" "%LOG_FILE%" >nul
    if %ERRORLEVEL% equ 0 (
        echo [OK] Compilacao bem-sucedida!
        
        REM Encontra o JAR gerado
        set "JAR_FILE="
        for %%f in (target\*.jar) do (
            if not "%%~nf"=="%%~nf.original" (
                set "JAR_FILE=%%f"
            )
        )
        
        if "!JAR_FILE!"=="" (
            echo ERRO: JAR nao encontrado!
            pause
            exit /b 1
        )
        
        echo JAR gerado: !JAR_FILE!
        echo.
        
        REM Executa a aplicacao
        echo ========================================
        echo  Iniciando aplicacao...
        echo ========================================
        echo.
        echo Para parar a aplicacao, pressione Ctrl+C
        echo.
        
        "%JAVA_HOME%\bin\java.exe" -jar "!JAR_FILE!"
        
    ) else (
        findstr /C:"BUILD FAILURE" "%LOG_FILE%" >nul
        if %ERRORLEVEL% equ 0 (
            echo [ERRO] Falha na compilacao!
            echo.
            echo Ultimas linhas do log:
            powershell -Command "Get-Content '%LOG_FILE%' | Select-Object -Last 10"
        ) else (
            echo [AVISO] Status da compilacao nao identificado
            echo.
            echo Log completo salvo em: %LOG_FILE%
        )
        pause
        exit /b 1
    )
) else (
    echo ERRO: Log de compilacao nao encontrado!
    pause
    exit /b 1
)

REM Limpa arquivo temporario
del "%LOG_FILE%" >nul 2>&1

echo.
echo Aplicacao finalizada
pause
