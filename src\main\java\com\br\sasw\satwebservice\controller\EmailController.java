package com.br.sasw.satwebservice.controller;

import com.br.sasw.satwebservice.repository.EmailsEnviarRepository;
import com.br.sasw.satwebservice.service.EmailSchedulerService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.Map;

@RestController
@RequestMapping("/emails")
@RequiredArgsConstructor
@Slf4j
public class EmailController {

    private final EmailSchedulerService schedulerService;
    private final EmailsEnviarRepository emailRepository;

    @GetMapping("/status")
    public ResponseEntity<EmailSchedulerService.SchedulerStatus> getStatus() {
        return ResponseEntity.ok(schedulerService.getStatus());
    }


    @PostMapping("/{sequencia}/reprocessar")
    public ResponseEntity<Map<String, String>> reprocessarEmail(@PathVariable String sequencia) {
        try {
            var email = emailRepository.findById(new BigDecimal(sequencia));
            if (email.isEmpty()) {
                return ResponseEntity.notFound().build();
            }

            emailRepository.marcarComoPendente(new BigDecimal(sequencia));

            log.info("Email sequência {} marcado para reprocessamento", sequencia);
            
            return ResponseEntity.ok(Map.of(
                "status", "success",
                "message", "Email marcado para reprocessamento"
            ));
            
        } catch (Exception e) {
            log.error("Erro ao reprocessar email {}: {}", sequencia, e.getMessage(), e);
            return ResponseEntity.badRequest().body(Map.of(
                "status", "error",
                "message", "Erro ao reprocessar email: " + e.getMessage()
            ));
        }
    }

}
