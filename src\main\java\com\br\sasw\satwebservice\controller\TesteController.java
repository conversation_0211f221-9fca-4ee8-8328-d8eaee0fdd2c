package com.br.sasw.satwebservice.controller;

import com.br.sasw.satwebservice.util.NFSeGoianiaXmlSigner;
import com.br.sasw.satwebservice.util.NFSeJoaoPessoaXmlSigner;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/nfse-jp")
public class TesteController {

    @PostMapping
    public String getSignedXml(@RequestBody String xml, @RequestParam String empresa) {

        return NFSeJoaoPessoaXmlSigner.sign(xml, empresa);
    }
}
