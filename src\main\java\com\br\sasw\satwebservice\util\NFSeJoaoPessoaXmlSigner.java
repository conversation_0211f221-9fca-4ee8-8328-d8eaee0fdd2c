package com.br.sasw.satwebservice.util;

import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.NodeList;
import org.xml.sax.InputSource;

import javax.xml.crypto.dsig.*;
import javax.xml.crypto.dsig.dom.DOMSignContext;
import javax.xml.crypto.dsig.keyinfo.KeyInfo;
import javax.xml.crypto.dsig.keyinfo.KeyInfoFactory;
import javax.xml.crypto.dsig.keyinfo.X509Data;
import javax.xml.crypto.dsig.spec.C14NMethodParameterSpec;
import javax.xml.crypto.dsig.spec.TransformParameterSpec;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.transform.Transformer;
import javax.xml.transform.TransformerFactory;
import javax.xml.transform.dom.DOMSource;
import javax.xml.transform.stream.StreamResult;
import java.io.ByteArrayInputStream;
import java.io.FileInputStream;
import java.io.StringWriter;
import java.security.KeyStore;
import java.security.PrivateKey;
import java.security.cert.X509Certificate;
import java.util.Arrays;
import java.util.Collections;

@UtilityClass
@Slf4j
public class NFSeJoaoPessoaXmlSigner {

    public String sign(String xml, String empresa) {
        try {
            log.debug("Iniciando assinatura digital do XML NFSe João Pessoa para empresa: {}", empresa);

            Certificado certificate = CarregarCertificados.buscaCertificadoPorChaveEmpresa(empresa);
            String caminhoCertificado = certificate.getLocal();
            String senhaCertificado = certificate.getSenha();

            KeyStore keyStore = KeyStore.getInstance("PKCS12");
            keyStore.load(new FileInputStream(caminhoCertificado), senhaCertificado.toCharArray());

            String alias = null;
            for (var e = keyStore.aliases(); e.hasMoreElements(); ) {
                alias = e.nextElement();
                if (keyStore.isKeyEntry(alias)) {
                    break;
                }
            }

            if (alias == null) {
                throw new RuntimeException("Nenhuma chave privada encontrada no certificado");
            }

            PrivateKey chavePrivada = (PrivateKey) keyStore.getKey(alias, senhaCertificado.toCharArray());
            X509Certificate certificado = (X509Certificate) keyStore.getCertificate(alias);

            log.debug("Certificado carregado com sucesso. Subject: {}", certificado.getSubjectDN());

            // Primeiro, vamos corrigir o XML adicionando o campo ExigibilidadeISS se não existir
            String xmlCorrigido = corrigirXmlJoaoPessoa(xml);

            DocumentBuilderFactory dbf = DocumentBuilderFactory.newInstance();
            dbf.setNamespaceAware(true);
            Document doc = dbf.newDocumentBuilder().parse(
                new InputSource(new ByteArrayInputStream(xmlCorrigido.getBytes("utf-8")))
            );

            NodeList infDeclaracaoList = doc.getElementsByTagName("InfDeclaracaoPrestacaoServico");
            if (infDeclaracaoList.getLength() == 0) {
                throw new RuntimeException("Elemento InfDeclaracaoPrestacaoServico não encontrado no XML");
            }

            Element infDeclaracaoElement = (Element) infDeclaracaoList.item(0);
            String idValue = infDeclaracaoElement.getAttribute("Id");
            
            if (idValue == null || idValue.isEmpty()) {
                throw new RuntimeException("Atributo Id não encontrado no elemento InfDeclaracaoPrestacaoServico");
            }

            return signNFSeDocument(doc, chavePrivada, certificado, "#" + idValue);

        } catch (Exception e) {
            log.error("Erro ao assinar XML de NFSe João Pessoa", e);
            throw new RuntimeException("Erro ao assinar XML de NFSe João Pessoa: " + e.getMessage(), e);
        }
    }

    /**
     * Corrige o XML para João Pessoa adicionando campos obrigatórios se não existirem
     */
    private String corrigirXmlJoaoPessoa(String xml) {
        // Adiciona ExigibilidadeISS se não existir
        if (!xml.contains("<ExigibilidadeISS>")) {
            // Adiciona ExigibilidadeISS=1 (Exigível no município) após CodigoMunicipio
            xml = xml.replaceAll("(<CodigoMunicipio>\\d+</CodigoMunicipio>)", 
                "$1\n                     <ExigibilidadeISS>1</ExigibilidadeISS>");
        }
        
        return xml;
    }

    /**
     * Realiza a assinatura do documento NFSe para João Pessoa
     */
    private String signNFSeDocument(Document doc, PrivateKey chavePrivada, X509Certificate certificado, String reference) 
            throws Exception {
        
        XMLSignatureFactory sigFactory = XMLSignatureFactory.getInstance("DOM");

        // Para João Pessoa, a referência deve apontar para o Id do InfDeclaracaoPrestacaoServico
        Reference ref = sigFactory.newReference(
            reference,
            sigFactory.newDigestMethod(DigestMethod.SHA256, null),
            Arrays.asList(
                sigFactory.newTransform(Transform.ENVELOPED, (TransformParameterSpec) null),
                sigFactory.newTransform(CanonicalizationMethod.INCLUSIVE, (C14NMethodParameterSpec) null)
            ),
            null,
            null
        );

        SignedInfo signedInfo = sigFactory.newSignedInfo(
            sigFactory.newCanonicalizationMethod(CanonicalizationMethod.INCLUSIVE, (C14NMethodParameterSpec) null),
            sigFactory.newSignatureMethod(SignatureMethod.RSA_SHA256, null),
            Collections.singletonList(ref)
        );

        KeyInfoFactory kif = sigFactory.getKeyInfoFactory();
        X509Data x509Data = kif.newX509Data(Collections.singletonList(certificado));
        KeyInfo keyInfo = kif.newKeyInfo(Collections.singletonList(x509Data));

        // Para João Pessoa, a assinatura deve ser inserida no elemento Rps
        NodeList rpsList = doc.getElementsByTagName("Rps");
        if (rpsList.getLength() == 0) {
            throw new RuntimeException("Elemento Rps não encontrado para inserir a assinatura");
        }
        
        Element rpsElement = (Element) rpsList.item(0);
        
        DOMSignContext dsc = new DOMSignContext(chavePrivada, rpsElement);

        XMLSignature signature = sigFactory.newXMLSignature(signedInfo, keyInfo);
        signature.sign(dsc);

        Transformer transformer = TransformerFactory.newInstance().newTransformer();
        transformer.setOutputProperty(javax.xml.transform.OutputKeys.INDENT, "no");
        transformer.setOutputProperty(javax.xml.transform.OutputKeys.ENCODING, "UTF-8");
        transformer.setOutputProperty(javax.xml.transform.OutputKeys.METHOD, "xml");
        transformer.setOutputProperty(javax.xml.transform.OutputKeys.OMIT_XML_DECLARATION, "yes");

        StringWriter writer = new StringWriter();
        transformer.transform(new DOMSource(doc), new StreamResult(writer));
        
        String result = writer.toString();
        log.debug("XML NFSe João Pessoa assinado com sucesso");
        
        return result;
    }
}
