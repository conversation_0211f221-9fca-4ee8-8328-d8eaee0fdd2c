package com.br.sasw.satwebservice.util;


import com.br.sasw.satwebservice.repository.CertificadoRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class CarregarCertificados {

    private static CertificadoRepository certificadoRepository;

    @Autowired
    public CarregarCertificados(CertificadoRepository repo) {
        CarregarCertificados.certificadoRepository = repo;
    }

    public static Certificado buscaCertificadoPorChaveEmpresa(String empresa) {
       return certificadoRepository.buscarPorEmpresa(empresa);
    }

//    public static Certificado buscaCertificadoPorChaveEmpresa(String empresa) {
//
//        Map<String, Certificado> certificados = Map.of(
//                "FEDERAL", new Certificado("C:\\certificados-chaves\\FEDERAL\\FEDERAL_GOIANIA.pfx", "Federallcom13"),
//                "INVIOSEG", new Certificado("C:\\certificados-chaves\\INVIOSEG\\INVIOSEG.pfx", "Invi#cer#25")
//        );
//
//        return certificados.get(empresa.toUpperCase());
//    }
}
